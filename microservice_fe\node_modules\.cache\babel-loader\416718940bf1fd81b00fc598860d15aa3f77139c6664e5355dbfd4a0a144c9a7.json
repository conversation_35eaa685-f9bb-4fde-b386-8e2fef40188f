{"ast": null, "code": "var _jsxFileName = \"D:\\\\HeThongCongTyQuanLyNhanCong\\\\Microservice_With_Kubernetes\\\\microservice_fe\\\\src\\\\components\\\\contract\\\\ContractAmountCalculation.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, Card, CardContent, Divider, Accordion, AccordionSummary, AccordionDetails, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Chip, useTheme } from '@mui/material';\nimport MonetizationOnIcon from '@mui/icons-material/MonetizationOn';\nimport ExpandMoreIcon from '@mui/icons-material/ExpandMore';\nimport CalculateIcon from '@mui/icons-material/Calculate';\nimport InfoIcon from '@mui/icons-material/Info';\nimport { calculateContractAmount } from '../../utils/contractCalculationUtils';\nimport { formatCurrency } from '../../utils/currencyUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ContractAmountCalculation({\n  contract\n}) {\n  _s();\n  const theme = useTheme();\n  const [expanded, setExpanded] = useState(false);\n  const calculation = calculateContractAmount(contract);\n  const handleExpandClick = () => {\n    setExpanded(!expanded);\n  };\n  if (calculation.totalAmount === 0) {\n    return /*#__PURE__*/_jsxDEV(Card, {\n      variant: \"outlined\",\n      sx: {\n        mb: 2,\n        borderColor: theme.palette.warning.light\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            mb: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(InfoIcon, {\n            sx: {\n              mr: 1,\n              color: theme.palette.warning.main\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            sx: {\n              fontWeight: 'bold',\n              color: theme.palette.warning.main\n            },\n            children: \"T\\u1ED5ng gi\\xE1 tr\\u1ECB h\\u1EE3p \\u0111\\u1ED3ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: \"Vui l\\xF2ng th\\xEAm chi ti\\u1EBFt c\\xF4ng vi\\u1EC7c v\\xE0 ca l\\xE0m vi\\u1EC7c \\u0111\\u1EC3 t\\xEDnh to\\xE1n t\\u1EF1 \\u0111\\u1ED9ng\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Card, {\n    variant: \"outlined\",\n    sx: {\n      mb: 2,\n      borderColor: theme.palette.success.light\n    },\n    children: /*#__PURE__*/_jsxDEV(CardContent, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(MonetizationOnIcon, {\n          sx: {\n            mr: 1,\n            color: theme.palette.success.main\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          sx: {\n            fontWeight: 'bold',\n            color: theme.palette.success.main\n          },\n          children: \"T\\u1ED5ng gi\\xE1 tr\\u1ECB h\\u1EE3p \\u0111\\u1ED3ng (T\\u1EF1 \\u0111\\u1ED9ng t\\xEDnh to\\xE1n)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          sx: {\n            fontWeight: 'bold',\n            color: theme.palette.success.main,\n            mr: 2\n          },\n          children: formatCurrency(calculation.totalAmount)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Chip, {\n          label: \"T\\u1EF1 \\u0111\\u1ED9ng\",\n          size: \"small\",\n          color: \"success\",\n          variant: \"outlined\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          mb: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexWrap: 'wrap',\n          gap: 2,\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"T\\u1ED5ng s\\u1ED1 ca l\\xE0m vi\\u1EC7c\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            sx: {\n              fontWeight: 'medium'\n            },\n            children: [calculation.summary.totalWorkShifts, \" ca\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"T\\u1ED5ng s\\u1ED1 nh\\xE2n c\\xF4ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            sx: {\n              fontWeight: 'medium'\n            },\n            children: [calculation.summary.totalWorkers, \" ng\\u01B0\\u1EDDi\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"T\\u1ED5ng ng\\xE0y l\\xE0m vi\\u1EC7c\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            sx: {\n              fontWeight: 'medium'\n            },\n            children: [calculation.summary.totalWorkingDays, \" ng\\xE0y\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Th\\u1EDDi gian h\\u1EE3p \\u0111\\u1ED3ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            sx: {\n              fontWeight: 'medium'\n            },\n            children: [calculation.summary.contractDuration, \" ng\\xE0y\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Accordion, {\n        expanded: expanded,\n        onChange: handleExpandClick,\n        sx: {\n          boxShadow: 'none'\n        },\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 25\n          }, this),\n          sx: {\n            backgroundColor: theme.palette.action.hover,\n            borderRadius: '4px',\n            minHeight: '36px',\n            '& .MuiAccordionSummary-content': {\n              margin: '4px 0'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(CalculateIcon, {\n              fontSize: \"small\",\n              sx: {\n                mr: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: \"Chi ti\\u1EBFt t\\xEDnh to\\xE1n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          sx: {\n            pt: 2,\n            pb: 1\n          },\n          children: calculation.breakdown.map((job, jobIndex) => /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mb: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              sx: {\n                fontWeight: 'bold',\n                mb: 1\n              },\n              children: job.jobCategoryName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n              sx: {\n                border: '1px solid #e0e0e0',\n                borderRadius: '4px',\n                mb: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Table, {\n                size: \"small\",\n                children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                  children: /*#__PURE__*/_jsxDEV(TableRow, {\n                    sx: {\n                      backgroundColor: theme.palette.action.hover\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Ca l\\xE0m vi\\u1EC7c\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 146,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"right\",\n                      children: \"L\\u01B0\\u01A1ng/ca\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 147,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"right\",\n                      children: \"S\\u1ED1 ng\\u01B0\\u1EDDi\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 148,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"right\",\n                      children: \"S\\u1ED1 ng\\xE0y\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 149,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"right\",\n                      children: \"Th\\xE0nh ti\\u1EC1n\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 150,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 145,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                  children: [job.workShifts.map((shift, shiftIndex) => /*#__PURE__*/_jsxDEV(TableRow, {\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                      children: [\"Ca \", shiftIndex + 1, \" (\", shift.startTime, \" - \", shift.endTime, \")\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 156,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"right\",\n                      children: formatCurrency(shift.salary)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 159,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"right\",\n                      children: shift.numberOfWorkers\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 162,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"right\",\n                      children: shift.workingDaysCount\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 165,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"right\",\n                      sx: {\n                        fontWeight: 'bold'\n                      },\n                      children: formatCurrency(shift.shiftAmount)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 168,\n                      columnNumber: 27\n                    }, this)]\n                  }, shiftIndex, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 155,\n                    columnNumber: 25\n                  }, this)), /*#__PURE__*/_jsxDEV(TableRow, {\n                    sx: {\n                      backgroundColor: theme.palette.action.hover\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                      colSpan: 4,\n                      sx: {\n                        fontWeight: 'bold'\n                      },\n                      children: [\"T\\u1ED5ng \", job.jobCategoryName]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 174,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"right\",\n                      sx: {\n                        fontWeight: 'bold',\n                        color: theme.palette.primary.main\n                      },\n                      children: formatCurrency(job.jobTotal)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 177,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 173,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 17\n            }, this)]\n          }, jobIndex, true, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 61,\n    columnNumber: 5\n  }, this);\n}\n_s(ContractAmountCalculation, \"3/7qUw4qpHcU2ewq0BO1T4qRkwQ=\", false, function () {\n  return [useTheme];\n});\n_c = ContractAmountCalculation;\n;\nexport default ContractAmountCalculation;\nvar _c;\n$RefreshReg$(_c, \"ContractAmountCalculation\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Divider", "Accordion", "AccordionSummary", "AccordionDetails", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Chip", "useTheme", "MonetizationOnIcon", "ExpandMoreIcon", "CalculateIcon", "InfoIcon", "calculateContractAmount", "formatCurrency", "jsxDEV", "_jsxDEV", "ContractAmountCalculation", "contract", "_s", "theme", "expanded", "setExpanded", "calculation", "handleExpandClick", "totalAmount", "variant", "sx", "mb", "borderColor", "palette", "warning", "light", "children", "display", "alignItems", "mr", "color", "main", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontWeight", "success", "label", "size", "flexWrap", "gap", "summary", "totalWorkShifts", "totalWorkers", "totalWorkingDays", "contractDuration", "onChange", "boxShadow", "expandIcon", "backgroundColor", "action", "hover", "borderRadius", "minHeight", "margin", "fontSize", "pt", "pb", "breakdown", "map", "job", "jobIndex", "jobCategoryName", "border", "align", "workShifts", "shift", "shiftIndex", "startTime", "endTime", "salary", "numberOfWorkers", "workingDaysCount", "shiftAmount", "colSpan", "primary", "jobTotal", "_c", "$RefreshReg$"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/contract/ContractAmountCalculation.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  Card,\n  CardContent,\n  Divider,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Chip,\n  useTheme,\n} from '@mui/material';\nimport MonetizationOnIcon from '@mui/icons-material/MonetizationOn';\nimport ExpandMoreIcon from '@mui/icons-material/ExpandMore';\nimport CalculateIcon from '@mui/icons-material/Calculate';\nimport InfoIcon from '@mui/icons-material/Info';\nimport { CustomerContract } from '../../models';\nimport { calculateContractAmount, ContractCalculationBreakdown } from '../../utils/contractCalculationUtils';\nimport { formatCurrency } from '../../utils/currencyUtils';\n\ninterface ContractAmountCalculationProps {\n  contract: Partial<CustomerContract>;\n}\n\nfunction ContractAmountCalculation({ contract }: ContractAmountCalculationProps) {\n  const theme = useTheme();\n  const [expanded, setExpanded] = useState(false);\n\n  const calculation = calculateContractAmount(contract);\n\n  const handleExpandClick = () => {\n    setExpanded(!expanded);\n  };\n\n  if (calculation.totalAmount === 0) {\n    return (\n      <Card variant=\"outlined\" sx={{ mb: 2, borderColor: theme.palette.warning.light }}>\n        <CardContent>\n          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n            <InfoIcon sx={{ mr: 1, color: theme.palette.warning.main }} />\n            <Typography variant=\"subtitle1\" sx={{ fontWeight: 'bold', color: theme.palette.warning.main }}>\n              Tổng giá trị hợp đồng\n            </Typography>\n          </Box>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            Vui lòng thêm chi tiết công việc và ca làm việc để tính toán tự động\n          </Typography>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <Card variant=\"outlined\" sx={{ mb: 2, borderColor: theme.palette.success.light }}>\n      <CardContent>\n        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n          <MonetizationOnIcon sx={{ mr: 1, color: theme.palette.success.main }} />\n          <Typography variant=\"subtitle1\" sx={{ fontWeight: 'bold', color: theme.palette.success.main }}>\n            Tổng giá trị hợp đồng (Tự động tính toán)\n          </Typography>\n        </Box>\n\n        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n          <Typography\n            variant=\"h4\"\n            sx={{\n              fontWeight: 'bold',\n              color: theme.palette.success.main,\n              mr: 2\n            }}\n          >\n            {formatCurrency(calculation.totalAmount)}\n          </Typography>\n          <Chip\n            label=\"Tự động\"\n            size=\"small\"\n            color=\"success\"\n            variant=\"outlined\"\n          />\n        </Box>\n\n        <Divider sx={{ mb: 2 }} />\n\n        {/* Summary */}\n        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 2 }}>\n          <Box>\n            <Typography variant=\"body2\" color=\"text.secondary\">Tổng số ca làm việc</Typography>\n            <Typography variant=\"body1\" sx={{ fontWeight: 'medium' }}>\n              {calculation.summary.totalWorkShifts} ca\n            </Typography>\n          </Box>\n          <Box>\n            <Typography variant=\"body2\" color=\"text.secondary\">Tổng số nhân công</Typography>\n            <Typography variant=\"body1\" sx={{ fontWeight: 'medium' }}>\n              {calculation.summary.totalWorkers} người\n            </Typography>\n          </Box>\n          <Box>\n            <Typography variant=\"body2\" color=\"text.secondary\">Tổng ngày làm việc</Typography>\n            <Typography variant=\"body1\" sx={{ fontWeight: 'medium' }}>\n              {calculation.summary.totalWorkingDays} ngày\n            </Typography>\n          </Box>\n          <Box>\n            <Typography variant=\"body2\" color=\"text.secondary\">Thời gian hợp đồng</Typography>\n            <Typography variant=\"body1\" sx={{ fontWeight: 'medium' }}>\n              {calculation.summary.contractDuration} ngày\n            </Typography>\n          </Box>\n        </Box>\n\n        {/* Detailed breakdown */}\n        <Accordion expanded={expanded} onChange={handleExpandClick} sx={{ boxShadow: 'none' }}>\n          <AccordionSummary\n            expandIcon={<ExpandMoreIcon />}\n            sx={{\n              backgroundColor: theme.palette.action.hover,\n              borderRadius: '4px',\n              minHeight: '36px',\n              '& .MuiAccordionSummary-content': { margin: '4px 0' }\n            }}\n          >\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <CalculateIcon fontSize=\"small\" sx={{ mr: 1 }} />\n              <Typography variant=\"body2\">Chi tiết tính toán</Typography>\n            </Box>\n          </AccordionSummary>\n          <AccordionDetails sx={{ pt: 2, pb: 1 }}>\n            {calculation.breakdown.map((job, jobIndex) => (\n              <Box key={jobIndex} sx={{ mb: 3 }}>\n                <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold', mb: 1 }}>\n                  {job.jobCategoryName}\n                </Typography>\n\n                <TableContainer sx={{ border: '1px solid #e0e0e0', borderRadius: '4px', mb: 2 }}>\n                  <Table size=\"small\">\n                    <TableHead>\n                      <TableRow sx={{ backgroundColor: theme.palette.action.hover }}>\n                        <TableCell>Ca làm việc</TableCell>\n                        <TableCell align=\"right\">Lương/ca</TableCell>\n                        <TableCell align=\"right\">Số người</TableCell>\n                        <TableCell align=\"right\">Số ngày</TableCell>\n                        <TableCell align=\"right\">Thành tiền</TableCell>\n                      </TableRow>\n                    </TableHead>\n                    <TableBody>\n                      {job.workShifts.map((shift, shiftIndex) => (\n                        <TableRow key={shiftIndex}>\n                          <TableCell>\n                            Ca {shiftIndex + 1} ({shift.startTime} - {shift.endTime})\n                          </TableCell>\n                          <TableCell align=\"right\">\n                            {formatCurrency(shift.salary)}\n                          </TableCell>\n                          <TableCell align=\"right\">\n                            {shift.numberOfWorkers}\n                          </TableCell>\n                          <TableCell align=\"right\">\n                            {shift.workingDaysCount}\n                          </TableCell>\n                          <TableCell align=\"right\" sx={{ fontWeight: 'bold' }}>\n                            {formatCurrency(shift.shiftAmount)}\n                          </TableCell>\n                        </TableRow>\n                      ))}\n                      <TableRow sx={{ backgroundColor: theme.palette.action.hover }}>\n                        <TableCell colSpan={4} sx={{ fontWeight: 'bold' }}>\n                          Tổng {job.jobCategoryName}\n                        </TableCell>\n                        <TableCell align=\"right\" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>\n                          {formatCurrency(job.jobTotal)}\n                        </TableCell>\n                      </TableRow>\n                    </TableBody>\n                  </Table>\n                </TableContainer>\n              </Box>\n            ))}\n          </AccordionDetails>\n        </Accordion>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default ContractAmountCalculation;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,OAAO,EACPC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,EAChBC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,IAAI,EACJC,QAAQ,QACH,eAAe;AACtB,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,QAAQ,MAAM,0BAA0B;AAE/C,SAASC,uBAAuB,QAAsC,sCAAsC;AAC5G,SAASC,cAAc,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM3D,SAASC,yBAAyBA,CAAC;EAAEC;AAAyC,CAAC,EAAE;EAAAC,EAAA;EAC/E,MAAMC,KAAK,GAAGZ,QAAQ,CAAC,CAAC;EACxB,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAE/C,MAAM+B,WAAW,GAAGV,uBAAuB,CAACK,QAAQ,CAAC;EAErD,MAAMM,iBAAiB,GAAGA,CAAA,KAAM;IAC9BF,WAAW,CAAC,CAACD,QAAQ,CAAC;EACxB,CAAC;EAED,IAAIE,WAAW,CAACE,WAAW,KAAK,CAAC,EAAE;IACjC,oBACET,OAAA,CAACrB,IAAI;MAAC+B,OAAO,EAAC,UAAU;MAACC,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,WAAW,EAAET,KAAK,CAACU,OAAO,CAACC,OAAO,CAACC;MAAM,CAAE;MAAAC,QAAA,eAC/EjB,OAAA,CAACpB,WAAW;QAAAqC,QAAA,gBACVjB,OAAA,CAACvB,GAAG;UAACkC,EAAE,EAAE;YAAEO,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEP,EAAE,EAAE;UAAE,CAAE;UAAAK,QAAA,gBACxDjB,OAAA,CAACJ,QAAQ;YAACe,EAAE,EAAE;cAAES,EAAE,EAAE,CAAC;cAAEC,KAAK,EAAEjB,KAAK,CAACU,OAAO,CAACC,OAAO,CAACO;YAAK;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9D1B,OAAA,CAACtB,UAAU;YAACgC,OAAO,EAAC,WAAW;YAACC,EAAE,EAAE;cAAEgB,UAAU,EAAE,MAAM;cAAEN,KAAK,EAAEjB,KAAK,CAACU,OAAO,CAACC,OAAO,CAACO;YAAK,CAAE;YAAAL,QAAA,EAAC;UAE/F;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACN1B,OAAA,CAACtB,UAAU;UAACgC,OAAO,EAAC,OAAO;UAACW,KAAK,EAAC,gBAAgB;UAAAJ,QAAA,EAAC;QAEnD;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEX;EAEA,oBACE1B,OAAA,CAACrB,IAAI;IAAC+B,OAAO,EAAC,UAAU;IAACC,EAAE,EAAE;MAAEC,EAAE,EAAE,CAAC;MAAEC,WAAW,EAAET,KAAK,CAACU,OAAO,CAACc,OAAO,CAACZ;IAAM,CAAE;IAAAC,QAAA,eAC/EjB,OAAA,CAACpB,WAAW;MAAAqC,QAAA,gBACVjB,OAAA,CAACvB,GAAG;QAACkC,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEP,EAAE,EAAE;QAAE,CAAE;QAAAK,QAAA,gBACxDjB,OAAA,CAACP,kBAAkB;UAACkB,EAAE,EAAE;YAAES,EAAE,EAAE,CAAC;YAAEC,KAAK,EAAEjB,KAAK,CAACU,OAAO,CAACc,OAAO,CAACN;UAAK;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxE1B,OAAA,CAACtB,UAAU;UAACgC,OAAO,EAAC,WAAW;UAACC,EAAE,EAAE;YAAEgB,UAAU,EAAE,MAAM;YAAEN,KAAK,EAAEjB,KAAK,CAACU,OAAO,CAACc,OAAO,CAACN;UAAK,CAAE;UAAAL,QAAA,EAAC;QAE/F;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEN1B,OAAA,CAACvB,GAAG;QAACkC,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEP,EAAE,EAAE;QAAE,CAAE;QAAAK,QAAA,gBACxDjB,OAAA,CAACtB,UAAU;UACTgC,OAAO,EAAC,IAAI;UACZC,EAAE,EAAE;YACFgB,UAAU,EAAE,MAAM;YAClBN,KAAK,EAAEjB,KAAK,CAACU,OAAO,CAACc,OAAO,CAACN,IAAI;YACjCF,EAAE,EAAE;UACN,CAAE;UAAAH,QAAA,EAEDnB,cAAc,CAACS,WAAW,CAACE,WAAW;QAAC;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACb1B,OAAA,CAACT,IAAI;UACHsC,KAAK,EAAC,wBAAS;UACfC,IAAI,EAAC,OAAO;UACZT,KAAK,EAAC,SAAS;UACfX,OAAO,EAAC;QAAU;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN1B,OAAA,CAACnB,OAAO;QAAC8B,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE;MAAE;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAG1B1B,OAAA,CAACvB,GAAG;QAACkC,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAEa,QAAQ,EAAE,MAAM;UAAEC,GAAG,EAAE,CAAC;UAAEpB,EAAE,EAAE;QAAE,CAAE;QAAAK,QAAA,gBAC5DjB,OAAA,CAACvB,GAAG;UAAAwC,QAAA,gBACFjB,OAAA,CAACtB,UAAU;YAACgC,OAAO,EAAC,OAAO;YAACW,KAAK,EAAC,gBAAgB;YAAAJ,QAAA,EAAC;UAAmB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACnF1B,OAAA,CAACtB,UAAU;YAACgC,OAAO,EAAC,OAAO;YAACC,EAAE,EAAE;cAAEgB,UAAU,EAAE;YAAS,CAAE;YAAAV,QAAA,GACtDV,WAAW,CAAC0B,OAAO,CAACC,eAAe,EAAC,KACvC;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACN1B,OAAA,CAACvB,GAAG;UAAAwC,QAAA,gBACFjB,OAAA,CAACtB,UAAU;YAACgC,OAAO,EAAC,OAAO;YAACW,KAAK,EAAC,gBAAgB;YAAAJ,QAAA,EAAC;UAAiB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACjF1B,OAAA,CAACtB,UAAU;YAACgC,OAAO,EAAC,OAAO;YAACC,EAAE,EAAE;cAAEgB,UAAU,EAAE;YAAS,CAAE;YAAAV,QAAA,GACtDV,WAAW,CAAC0B,OAAO,CAACE,YAAY,EAAC,kBACpC;UAAA;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACN1B,OAAA,CAACvB,GAAG;UAAAwC,QAAA,gBACFjB,OAAA,CAACtB,UAAU;YAACgC,OAAO,EAAC,OAAO;YAACW,KAAK,EAAC,gBAAgB;YAAAJ,QAAA,EAAC;UAAkB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAClF1B,OAAA,CAACtB,UAAU;YAACgC,OAAO,EAAC,OAAO;YAACC,EAAE,EAAE;cAAEgB,UAAU,EAAE;YAAS,CAAE;YAAAV,QAAA,GACtDV,WAAW,CAAC0B,OAAO,CAACG,gBAAgB,EAAC,UACxC;UAAA;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACN1B,OAAA,CAACvB,GAAG;UAAAwC,QAAA,gBACFjB,OAAA,CAACtB,UAAU;YAACgC,OAAO,EAAC,OAAO;YAACW,KAAK,EAAC,gBAAgB;YAAAJ,QAAA,EAAC;UAAkB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAClF1B,OAAA,CAACtB,UAAU;YAACgC,OAAO,EAAC,OAAO;YAACC,EAAE,EAAE;cAAEgB,UAAU,EAAE;YAAS,CAAE;YAAAV,QAAA,GACtDV,WAAW,CAAC0B,OAAO,CAACI,gBAAgB,EAAC,UACxC;UAAA;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN1B,OAAA,CAAClB,SAAS;QAACuB,QAAQ,EAAEA,QAAS;QAACiC,QAAQ,EAAE9B,iBAAkB;QAACG,EAAE,EAAE;UAAE4B,SAAS,EAAE;QAAO,CAAE;QAAAtB,QAAA,gBACpFjB,OAAA,CAACjB,gBAAgB;UACfyD,UAAU,eAAExC,OAAA,CAACN,cAAc;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC/Bf,EAAE,EAAE;YACF8B,eAAe,EAAErC,KAAK,CAACU,OAAO,CAAC4B,MAAM,CAACC,KAAK;YAC3CC,YAAY,EAAE,KAAK;YACnBC,SAAS,EAAE,MAAM;YACjB,gCAAgC,EAAE;cAAEC,MAAM,EAAE;YAAQ;UACtD,CAAE;UAAA7B,QAAA,eAEFjB,OAAA,CAACvB,GAAG;YAACkC,EAAE,EAAE;cAAEO,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE;YAAS,CAAE;YAAAF,QAAA,gBACjDjB,OAAA,CAACL,aAAa;cAACoD,QAAQ,EAAC,OAAO;cAACpC,EAAE,EAAE;gBAAES,EAAE,EAAE;cAAE;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjD1B,OAAA,CAACtB,UAAU;cAACgC,OAAO,EAAC,OAAO;cAAAO,QAAA,EAAC;YAAkB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC,eACnB1B,OAAA,CAAChB,gBAAgB;UAAC2B,EAAE,EAAE;YAAEqC,EAAE,EAAE,CAAC;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAhC,QAAA,EACpCV,WAAW,CAAC2C,SAAS,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEC,QAAQ,kBACvCrD,OAAA,CAACvB,GAAG;YAAgBkC,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAK,QAAA,gBAChCjB,OAAA,CAACtB,UAAU;cAACgC,OAAO,EAAC,WAAW;cAACC,EAAE,EAAE;gBAAEgB,UAAU,EAAE,MAAM;gBAAEf,EAAE,EAAE;cAAE,CAAE;cAAAK,QAAA,EAC/DmC,GAAG,CAACE;YAAe;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEb1B,OAAA,CAACZ,cAAc;cAACuB,EAAE,EAAE;gBAAE4C,MAAM,EAAE,mBAAmB;gBAAEX,YAAY,EAAE,KAAK;gBAAEhC,EAAE,EAAE;cAAE,CAAE;cAAAK,QAAA,eAC9EjB,OAAA,CAACf,KAAK;gBAAC6C,IAAI,EAAC,OAAO;gBAAAb,QAAA,gBACjBjB,OAAA,CAACX,SAAS;kBAAA4B,QAAA,eACRjB,OAAA,CAACV,QAAQ;oBAACqB,EAAE,EAAE;sBAAE8B,eAAe,EAAErC,KAAK,CAACU,OAAO,CAAC4B,MAAM,CAACC;oBAAM,CAAE;oBAAA1B,QAAA,gBAC5DjB,OAAA,CAACb,SAAS;sBAAA8B,QAAA,EAAC;oBAAW;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAClC1B,OAAA,CAACb,SAAS;sBAACqE,KAAK,EAAC,OAAO;sBAAAvC,QAAA,EAAC;oBAAQ;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC7C1B,OAAA,CAACb,SAAS;sBAACqE,KAAK,EAAC,OAAO;sBAAAvC,QAAA,EAAC;oBAAQ;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC7C1B,OAAA,CAACb,SAAS;sBAACqE,KAAK,EAAC,OAAO;sBAAAvC,QAAA,EAAC;oBAAO;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC5C1B,OAAA,CAACb,SAAS;sBAACqE,KAAK,EAAC,OAAO;sBAAAvC,QAAA,EAAC;oBAAU;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACZ1B,OAAA,CAACd,SAAS;kBAAA+B,QAAA,GACPmC,GAAG,CAACK,UAAU,CAACN,GAAG,CAAC,CAACO,KAAK,EAAEC,UAAU,kBACpC3D,OAAA,CAACV,QAAQ;oBAAA2B,QAAA,gBACPjB,OAAA,CAACb,SAAS;sBAAA8B,QAAA,GAAC,KACN,EAAC0C,UAAU,GAAG,CAAC,EAAC,IAAE,EAACD,KAAK,CAACE,SAAS,EAAC,KAAG,EAACF,KAAK,CAACG,OAAO,EAAC,GAC1D;oBAAA;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eACZ1B,OAAA,CAACb,SAAS;sBAACqE,KAAK,EAAC,OAAO;sBAAAvC,QAAA,EACrBnB,cAAc,CAAC4D,KAAK,CAACI,MAAM;oBAAC;sBAAAvC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpB,CAAC,eACZ1B,OAAA,CAACb,SAAS;sBAACqE,KAAK,EAAC,OAAO;sBAAAvC,QAAA,EACrByC,KAAK,CAACK;oBAAe;sBAAAxC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb,CAAC,eACZ1B,OAAA,CAACb,SAAS;sBAACqE,KAAK,EAAC,OAAO;sBAAAvC,QAAA,EACrByC,KAAK,CAACM;oBAAgB;sBAAAzC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACd,CAAC,eACZ1B,OAAA,CAACb,SAAS;sBAACqE,KAAK,EAAC,OAAO;sBAAC7C,EAAE,EAAE;wBAAEgB,UAAU,EAAE;sBAAO,CAAE;sBAAAV,QAAA,EACjDnB,cAAc,CAAC4D,KAAK,CAACO,WAAW;oBAAC;sBAAA1C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB,CAAC;kBAAA,GAfCiC,UAAU;oBAAApC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAgBf,CACX,CAAC,eACF1B,OAAA,CAACV,QAAQ;oBAACqB,EAAE,EAAE;sBAAE8B,eAAe,EAAErC,KAAK,CAACU,OAAO,CAAC4B,MAAM,CAACC;oBAAM,CAAE;oBAAA1B,QAAA,gBAC5DjB,OAAA,CAACb,SAAS;sBAAC+E,OAAO,EAAE,CAAE;sBAACvD,EAAE,EAAE;wBAAEgB,UAAU,EAAE;sBAAO,CAAE;sBAAAV,QAAA,GAAC,YAC5C,EAACmC,GAAG,CAACE,eAAe;oBAAA;sBAAA/B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChB,CAAC,eACZ1B,OAAA,CAACb,SAAS;sBAACqE,KAAK,EAAC,OAAO;sBAAC7C,EAAE,EAAE;wBAAEgB,UAAU,EAAE,MAAM;wBAAEN,KAAK,EAAEjB,KAAK,CAACU,OAAO,CAACqD,OAAO,CAAC7C;sBAAK,CAAE;sBAAAL,QAAA,EACpFnB,cAAc,CAACsD,GAAG,CAACgB,QAAQ;oBAAC;sBAAA7C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC;UAAA,GA9CT2B,QAAQ;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA+Cb,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEX;AAACvB,EAAA,CA/JQF,yBAAyB;EAAA,QAClBT,QAAQ;AAAA;AAAA6E,EAAA,GADfpE,yBAAyB;AA+JjC;AAED,eAAeA,yBAAyB;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}