version: '3.8'

services:

  # API Gateway
  api-gateway:
    build:
      context: ./api-gateway
      dockerfile: Dockerfile
    container_name: api-gateway
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_MAIN_ALLOW_BEAN_DEFINITION_OVERRIDING=true
    networks:
      - microservice-network
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # Customer Service
  customer-service:
    build:
      context: ./customer-service
      dockerfile: Dockerfile
    container_name: customer-service
    ports:
      - "8081:8081"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_DATASOURCE_URL=******************************************************
      - SPRING_DATASOURCE_USERNAME=postgres
      - SPRING_DATASOURCE_PASSWORD=1234
      - SPRING_JPA_HIBERNATE_DDL_AUTO=validate
      - SPRING_MAIN_ALLOW_BEAN_DEFINITION_OVERRIDING=true
      - SPRING_SQL_INIT_MODE=never
      - SERVER_PORT=8081
    networks:
      - microservice-network
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # Job Service
  job-service:
    build:
      context: ./job-service
      dockerfile: Dockerfile
    container_name: job-service
    ports:
      - "8082:8082"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_DATASOURCE_URL=*************************************************
      - SPRING_DATASOURCE_USERNAME=postgres
      - SPRING_DATASOURCE_PASSWORD=1234
      - SPRING_JPA_HIBERNATE_DDL_AUTO=validate
      - SPRING_MAIN_ALLOW_BEAN_DEFINITION_OVERRIDING=true
      - SPRING_SQL_INIT_MODE=never
      - SERVER_PORT=8082
    networks:
      - microservice-network
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # Customer Contract Service
  customer-contract-service:
    build:
      context: ./customer-contract-service
      dockerfile: Dockerfile
    container_name: customer-contract-service
    ports:
      - "8083:8083"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_DATASOURCE_URL=**************************************************************
      - SPRING_DATASOURCE_USERNAME=postgres
      - SPRING_DATASOURCE_PASSWORD=1234
      - SPRING_JPA_HIBERNATE_DDL_AUTO=validate
      - SPRING_MAIN_ALLOW_BEAN_DEFINITION_OVERRIDING=true
      - SPRING_SQL_INIT_MODE=never
      - SERVER_PORT=8083
      - CUSTOMER_SERVICE_URL=http://customer-service:8081/api/customer
      - JOB_SERVICE_URL=http://job-service:8082/api/job
      - JOB_CATEGORY_SERVICE_URL=http://job-service:8082/api/job-category
    networks:
      - microservice-network
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # Customer Payment Service
  customer-payment-service:
    build:
      context: ./customer-payment-service
      dockerfile: Dockerfile
    container_name: customer-payment-service
    ports:
      - "8084:8084"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_DATASOURCE_URL=*************************************************************
      - SPRING_DATASOURCE_USERNAME=postgres
      - SPRING_DATASOURCE_PASSWORD=1234
      - SPRING_JPA_HIBERNATE_DDL_AUTO=validate
      - SPRING_MAIN_ALLOW_BEAN_DEFINITION_OVERRIDING=true
      - SPRING_SQL_INIT_MODE=never
      - SERVER_PORT=8084
      - CUSTOMER_SERVICE_URL=http://customer-service:8081/api/customer
      - CUSTOMERCONTRACT_SERVICE_URL=http://customer-contract-service:8083/api/customer-contract
    networks:
      - microservice-network
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # Customer Statistics Service
  customer-statistics-service:
    build:
      context: ./customer-statistics-service
      dockerfile: Dockerfile
    container_name: customer-statistics-service
    ports:
      - "8085:8085"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_MAIN_ALLOW_BEAN_DEFINITION_OVERRIDING=true
      - SERVER_PORT=8085
      - APP_CUSTOMER-SERVICE_URL=http://customer-service:8081/api/customer
      - APP_CUSTOMER-CONTRACT-SERVICE_URL=http://customer-contract-service:8083/api/customer-contract
      - APP_CUSTOMER-PAYMENT-SERVICE_URL=http://customer-payment-service:8084/api/customer-payment
    networks:
      - microservice-network
    extra_hosts:
      - "host.docker.internal:host-gateway"

networks:
  microservice-network:
    driver: bridge
